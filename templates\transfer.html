{% extends "base.html" %}

{% block title %}Transfer Funds - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-exchange-alt"></i> Transfer Funds
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill-transfer"></i> Fund Transfer
            </div>
            <div class="card-body">
                <form method="POST" id="transferForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="from_account" class="form-label">
                                    <i class="fas fa-arrow-up"></i> From Account *
                                </label>
                                <input type="number" class="form-control" id="from_account" name="from_account" required>
                                <div class="form-text">Source account number</div>
                                <div id="from-account-info" class="mt-2"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="to_account" class="form-label">
                                    <i class="fas fa-arrow-down"></i> To Account *
                                </label>
                                <input type="number" class="form-control" id="to_account" name="to_account" required>
                                <div class="form-text">Destination account number</div>
                                <div id="to-account-info" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">
                            <i class="fas fa-dollar-sign"></i> Transfer Amount *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   min="0.01" step="0.01" required>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> Daily transfer limit: ₹50,000 per account
                        </div>
                        <div id="transfer-info" class="mt-2"></div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-info btn-lg" id="transferBtn" disabled>
                            <i class="fas fa-exchange-alt"></i> Transfer Funds
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Transfer Information
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Instant processing</li>
                    <li><i class="fas fa-check text-success"></i> Daily limit: ₹50,000 per account</li>
                    <li><i class="fas fa-check text-success"></i> Minimum balance maintained</li>
                    <li><i class="fas fa-check text-success"></i> Complete transaction logging</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security Features
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-lock text-primary"></i> Secure transaction processing</li>
                    <li><i class="fas fa-check-double text-primary"></i> Account verification</li>
                    <li><i class="fas fa-balance-scale text-primary"></i> Balance validation</li>
                    <li><i class="fas fa-history text-primary"></i> Audit trail</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Process -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-route"></i> Transfer Process
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="fas fa-user-check fa-2x text-primary"></i>
                            <h6 class="mt-2">Verify Accounts</h6>
                            <p class="small text-muted">Both accounts are validated</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="fas fa-calculator fa-2x text-info"></i>
                            <h6 class="mt-2">Check Balance</h6>
                            <p class="small text-muted">Sufficient funds verified</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="fas fa-exchange-alt fa-2x text-success"></i>
                            <h6 class="mt-2">Process Transfer</h6>
                            <p class="small text-muted">Funds transferred instantly</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <i class="fas fa-file-alt fa-2x text-warning"></i>
                            <h6 class="mt-2">Log Transaction</h6>
                            <p class="small text-muted">Complete audit trail created</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let fromAccountData = null;
let toAccountData = null;

// Validate account function
function validateAccount(accountNumber, infoElementId, isFromAccount = true) {
    const accountInfo = document.getElementById(infoElementId);
    
    if (accountNumber) {
        fetch('/api/account/' + accountNumber)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle"></i> Account not found</div>';
                    if (isFromAccount) fromAccountData = null;
                    else toAccountData = null;
                } else if (data.status !== 'Active') {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-ban"></i> Account is not active</div>';
                    if (isFromAccount) fromAccountData = null;
                    else toAccountData = null;
                } else {
                    if (isFromAccount) {
                        fromAccountData = data;
                        const minBalance = data.account_type === 'Savings' ? 500 : 1000;
                        const globalMin = 500;
                        const effectiveMin = Math.max(minBalance, globalMin);
                        const maxTransfer = Math.max(0, data.balance - effectiveMin);
                        
                        accountInfo.innerHTML = `
                            <div class="alert alert-success alert-sm">
                                <i class="fas fa-user"></i> <strong>${data.name}</strong> (${data.account_type})<br>
                                <i class="fas fa-wallet"></i> Balance: ₹${parseFloat(data.balance).toFixed(2)}<br>
                                <i class="fas fa-arrow-up"></i> Max Transfer: ₹${maxTransfer.toFixed(2)}
                            </div>
                        `;
                        
                        // Update amount input max value
                        document.getElementById('amount').max = maxTransfer;
                    } else {
                        toAccountData = data;
                        accountInfo.innerHTML = `
                            <div class="alert alert-success alert-sm">
                                <i class="fas fa-user"></i> <strong>${data.name}</strong> (${data.account_type})<br>
                                <i class="fas fa-wallet"></i> Balance: ₹${parseFloat(data.balance).toFixed(2)}
                            </div>
                        `;
                    }
                }
                updateTransferButton();
            })
            .catch(error => {
                accountInfo.innerHTML = '<div class="alert alert-warning alert-sm"><i class="fas fa-exclamation-triangle"></i> Unable to fetch account information</div>';
                if (isFromAccount) fromAccountData = null;
                else toAccountData = null;
                updateTransferButton();
            });
    } else {
        accountInfo.innerHTML = '';
        if (isFromAccount) fromAccountData = null;
        else toAccountData = null;
        updateTransferButton();
    }
}

// Update transfer button state
function updateTransferButton() {
    const transferBtn = document.getElementById('transferBtn');
    const fromAccount = document.getElementById('from_account').value;
    const toAccount = document.getElementById('to_account').value;
    
    if (fromAccountData && toAccountData && fromAccount !== toAccount) {
        transferBtn.disabled = false;
        transferBtn.classList.remove('btn-secondary');
        transferBtn.classList.add('btn-info');
    } else {
        transferBtn.disabled = true;
        transferBtn.classList.remove('btn-info');
        transferBtn.classList.add('btn-secondary');
    }
}

// Event listeners
document.getElementById('from_account').addEventListener('blur', function() {
    validateAccount(this.value, 'from-account-info', true);
});

document.getElementById('to_account').addEventListener('blur', function() {
    validateAccount(this.value, 'to-account-info', false);
});

// Check for same account
document.getElementById('to_account').addEventListener('input', function() {
    const fromAccount = document.getElementById('from_account').value;
    const transferInfo = document.getElementById('transfer-info');
    
    if (this.value === fromAccount && this.value !== '') {
        transferInfo.innerHTML = '<div class="alert alert-warning alert-sm"><i class="fas fa-exclamation-triangle"></i> Cannot transfer to the same account</div>';
    } else {
        transferInfo.innerHTML = '';
    }
    updateTransferButton();
});

// Form validation
document.getElementById('transferForm').addEventListener('submit', function(e) {
    const fromAccount = document.getElementById('from_account').value;
    const toAccount = document.getElementById('to_account').value;
    const amount = parseFloat(document.getElementById('amount').value);
    
    if (fromAccount === toAccount) {
        e.preventDefault();
        alert('Cannot transfer to the same account');
        return;
    }
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Transfer amount must be positive');
        return;
    }
    
    if (fromAccountData) {
        const minBalance = fromAccountData.account_type === 'Savings' ? 500 : 1000;
        const globalMin = 500;
        const effectiveMin = Math.max(minBalance, globalMin);
        const maxTransfer = Math.max(0, fromAccountData.balance - effectiveMin);
        
        if (amount > maxTransfer) {
            e.preventDefault();
            alert(`Transfer amount exceeds available balance. Maximum transfer: ₹${maxTransfer.toFixed(2)}`);
            return;
        }
    }
});
</script>
{% endblock %}
