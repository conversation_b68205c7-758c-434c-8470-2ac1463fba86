#!/usr/bin/env python3
"""
Test script to verify PIN functionality in the Flask app
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_pin_functionality():
    print("Testing PIN Management Functionality")
    print("=" * 50)
    
    session = requests.Session()
    
    # Test 1: Check PIN management page loads
    try:
        response = session.get(f"{BASE_URL}/pin_management")
        print(f"✓ PIN Management page: Status {response.status_code}")
    except Exception as e:
        print(f"✗ PIN Management page failed: {e}")
        return
    
    # Test 2: Check PIN status API for existing account
    try:
        response = session.get(f"{BASE_URL}/api/account/1001/pin_status")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ PIN Status API: Account {data['account_number']} - PIN: {'Set' if data['has_pin'] else 'Not Set'}")
        else:
            print(f"? PIN Status API: Status {response.status_code}")
    except Exception as e:
        print(f"✗ PIN Status API failed: {e}")
    
    # Test 3: Test PIN status for non-existent account
    try:
        response = session.get(f"{BASE_URL}/api/account/9999/pin_status")
        if response.status_code == 404:
            print("✓ PIN Status API: Correctly handles non-existent account")
        else:
            print(f"? PIN Status API for invalid account: Status {response.status_code}")
    except Exception as e:
        print(f"✗ PIN Status API for invalid account failed: {e}")
    
    # Test 4: Try to set a PIN for account 1001
    try:
        pin_data = {
            'action': 'set_pin',
            'account_number': '1001',
            'new_pin': '1234',
            'confirm_pin': '1234'
        }
        
        response = session.post(f"{BASE_URL}/pin_management", data=pin_data, allow_redirects=False)
        if response.status_code in [200, 302, 303]:
            print("✓ Set PIN: Request processed successfully")
        else:
            print(f"? Set PIN: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Set PIN failed: {e}")
    
    # Test 5: Try to verify the PIN
    try:
        verify_data = {
            'action': 'verify_pin',
            'account_number': '1001',
            'pin': '1234'
        }
        
        response = session.post(f"{BASE_URL}/pin_management", data=verify_data, allow_redirects=False)
        if response.status_code in [200, 302, 303]:
            print("✓ Verify PIN: Request processed successfully")
        else:
            print(f"? Verify PIN: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Verify PIN failed: {e}")
    
    # Test 6: Try to change the PIN
    try:
        change_data = {
            'action': 'change_pin',
            'account_number': '1001',
            'old_pin': '1234',
            'new_pin': '5678',
            'confirm_pin': '5678'
        }
        
        response = session.post(f"{BASE_URL}/pin_management", data=change_data, allow_redirects=False)
        if response.status_code in [200, 302, 303]:
            print("✓ Change PIN: Request processed successfully")
        else:
            print(f"? Change PIN: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Change PIN failed: {e}")
    
    # Test 7: Check PIN status after changes
    try:
        response = session.get(f"{BASE_URL}/api/account/1001/pin_status")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Final PIN Status: Account {data['account_number']} - PIN: {'Set' if data['has_pin'] else 'Not Set'}")
        else:
            print(f"? Final PIN Status: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Final PIN Status failed: {e}")
    
    # Test 8: Test PIN management with URL parameter
    try:
        response = session.get(f"{BASE_URL}/pin_management?account=1001")
        print(f"✓ PIN Management with URL parameter: Status {response.status_code}")
    except Exception as e:
        print(f"✗ PIN Management with URL parameter failed: {e}")
    
    print("\n" + "=" * 50)
    print("PIN functionality test completed!")
    print("Check the browser at http://localhost:5000/pin_management")
    print("All PIN management features should be working correctly.")

if __name__ == "__main__":
    test_pin_functionality()
