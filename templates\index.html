{% extends "base.html" %}

{% block title %}Dashboard - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Banking Dashboard
        </h1>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Create New Account
                    </a>
                    <a href="{{ url_for('deposit') }}" class="btn btn-success">
                        <i class="fas fa-plus-circle"></i> Make Deposit
                    </a>
                    <a href="{{ url_for('withdraw') }}" class="btn btn-warning">
                        <i class="fas fa-minus-circle"></i> Make Withdrawal
                    </a>
                    <a href="{{ url_for('transfer') }}" class="btn btn-info">
                        <i class="fas fa-exchange-alt"></i> Transfer Funds
                    </a>
                    <a href="{{ url_for('pin_management') }}" class="btn btn-secondary">
                        <i class="fas fa-lock"></i> PIN Management
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Management -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-users"></i> Account Management
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> View All Accounts
                    </a>
                    <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> Search Accounts
                    </a>
                    <a href="{{ url_for('balance_inquiry') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search-dollar"></i> Balance Inquiry
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports & Analytics -->
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Reports & Analytics
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('analytics') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-analytics"></i> View Analytics
                    </a>
                    <button class="btn btn-outline-secondary" onclick="exportAccounts()">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Overview -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-star"></i> Banking Features
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Basic Operations</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Account Creation & Management</li>
                            <li><i class="fas fa-check text-success"></i> Deposits & Withdrawals</li>
                            <li><i class="fas fa-check text-success"></i> Balance Inquiries</li>
                            <li><i class="fas fa-check text-success"></i> Account Closure & Reopening</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Advanced Features</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Fund Transfers</li>
                            <li><i class="fas fa-check text-success"></i> Transaction History</li>
                            <li><i class="fas fa-check text-success"></i> Interest Calculations</li>
                            <li><i class="fas fa-check text-success"></i> Account Analytics</li>
                            <li><i class="fas fa-check text-success"></i> PIN Management</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> System Information
            </div>
            <div class="card-body">
                <p><strong>Minimum Balance:</strong></p>
                <ul>
                    <li>Savings Account: ₹500</li>
                    <li>Current Account: ₹1,000</li>
                </ul>
                <p><strong>Daily Transaction Limit:</strong> ₹50,000</p>
                <p><strong>Maximum Single Deposit:</strong> ₹1,00,000</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security Features
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-lock text-primary"></i> PIN Protection</li>
                    <li><i class="fas fa-history text-primary"></i> Transaction Logging</li>
                    <li><i class="fas fa-user-shield text-primary"></i> Age Verification</li>
                    <li><i class="fas fa-database text-primary"></i> Data Persistence</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function exportAccounts() {
    // This would typically make an AJAX call to export accounts
    alert('Export functionality would be implemented here');
}
</script>
{% endblock %}
