{% extends "base.html" %}

{% block title %}Dashboard - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2 fade-in-up">
                    <i class="fas fa-tachometer-alt text-primary"></i> Banking Dashboard
                </h1>
                <p class="text-muted fade-in-up">Welcome to your comprehensive banking management system</p>
            </div>
            <div class="fade-in-up">
                <div class="d-flex align-items-center">
                    <span class="badge bg-success me-2">
                        <i class="fas fa-circle"></i> System Online
                    </span>
                    <small class="text-muted">Last updated: {{ moment().format('MMM DD, YYYY HH:mm') if moment else 'Now' }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.1s">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i> Create New Account
                    </a>
                    <a href="{{ url_for('deposit') }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-2"></i> Make Deposit
                    </a>
                    <a href="{{ url_for('withdraw') }}" class="btn btn-warning">
                        <i class="fas fa-minus-circle me-2"></i> Make Withdrawal
                    </a>
                    <a href="{{ url_for('transfer') }}" class="btn btn-info">
                        <i class="fas fa-exchange-alt me-2"></i> Transfer Funds
                    </a>
                    <a href="{{ url_for('pin_management') }}" class="btn btn-secondary">
                        <i class="fas fa-lock me-2"></i> PIN Management
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Management -->
    <div class="col-lg-4 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.2s">
            <div class="card-header">
                <i class="fas fa-users"></i> Account Management
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i> View All Accounts
                    </a>
                    <a href="{{ url_for('search') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i> Search Accounts
                    </a>
                    <a href="{{ url_for('balance_inquiry') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search-dollar me-2"></i> Balance Inquiry
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports & Analytics -->
    <div class="col-lg-4 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.3s">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Reports & Analytics
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('analytics') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-line me-2"></i> View Analytics
                    </a>
                    <button class="btn btn-outline-secondary" onclick="exportAccounts()">
                        <i class="fas fa-download me-2"></i> Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feature Highlights -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card fade-in-up" style="animation-delay: 0.4s">
            <div class="card-header">
                <i class="fas fa-star"></i> Banking Features
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 50px; height: 50px;">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <h6 class="text-primary">Account Creation</h6>
                            <p class="small text-muted mb-0">Create savings & current accounts with instant activation</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); width: 50px; height: 50px;">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <h6 class="text-info">Fund Transfers</h6>
                            <p class="small text-muted mb-0">Instant transfers between accounts with real-time processing</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); width: 50px; height: 50px;">
                                <i class="fas fa-lock"></i>
                            </div>
                            <h6 class="text-success">PIN Security</h6>
                            <p class="small text-muted mb-0">Secure 4-digit PIN protection for all accounts</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); width: 50px; height: 50px;">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h6 class="text-warning">Analytics</h6>
                            <p class="small text-muted mb-0">Comprehensive insights and transaction analytics</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.5s">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> System Information
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-2 mb-3" style="background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                            <h6 class="text-primary mb-1">₹500</h6>
                            <small class="text-muted">Savings Min Balance</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2 mb-3" style="background: rgba(79, 172, 254, 0.1); border-radius: 8px;">
                            <h6 class="text-info mb-1">₹1,000</h6>
                            <small class="text-muted">Current Min Balance</small>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center p-2" style="background: rgba(67, 233, 123, 0.1); border-radius: 8px;">
                            <h6 class="text-success mb-1">₹50,000</h6>
                            <small class="text-muted">Daily Limit</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center p-2" style="background: rgba(250, 112, 154, 0.1); border-radius: 8px;">
                            <h6 class="text-warning mb-1">₹1,00,000</h6>
                            <small class="text-muted">Max Deposit</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.6s">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security & Features
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3 p-2" style="background: rgba(102, 126, 234, 0.05); border-radius: 8px;">
                    <i class="fas fa-lock text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">PIN Protection</h6>
                        <small class="text-muted">4-digit secure PIN for all accounts</small>
                    </div>
                </div>
                <div class="d-flex align-items-center mb-3 p-2" style="background: rgba(79, 172, 254, 0.05); border-radius: 8px;">
                    <i class="fas fa-history text-info me-3"></i>
                    <div>
                        <h6 class="mb-1">Transaction Logging</h6>
                        <small class="text-muted">Complete audit trail for all operations</small>
                    </div>
                </div>
                <div class="d-flex align-items-center mb-3 p-2" style="background: rgba(67, 233, 123, 0.05); border-radius: 8px;">
                    <i class="fas fa-user-shield text-success me-3"></i>
                    <div>
                        <h6 class="mb-1">Age Verification</h6>
                        <small class="text-muted">Minimum 18 years requirement</small>
                    </div>
                </div>
                <div class="d-flex align-items-center p-2" style="background: rgba(250, 112, 154, 0.05); border-radius: 8px;">
                    <i class="fas fa-database text-warning me-3"></i>
                    <div>
                        <h6 class="mb-1">Data Persistence</h6>
                        <small class="text-muted">Secure CSV-based storage system</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function exportAccounts() {
    const exportBtn = event.target;
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';
    exportBtn.disabled = true;

    // Simulate export process
    setTimeout(() => {
        // In a real application, this would make an AJAX call to the backend
        alert('Export functionality would be implemented here.\nThis would download a CSV file with all account data.');
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }, 2000);
}

// Add interactive animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate metric cards on hover
    document.querySelectorAll('.metric-card, .card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add click animations to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add floating animation to feature icons
    setInterval(() => {
        document.querySelectorAll('.metric-icon').forEach((icon, index) => {
            setTimeout(() => {
                icon.style.transform = 'translateY(-2px)';
                setTimeout(() => {
                    icon.style.transform = 'translateY(0)';
                }, 1000);
            }, index * 200);
        });
    }, 3000);
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .metric-icon {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .fade-in-up {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeInUp 0.6s ease-out forwards;
    }

    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
