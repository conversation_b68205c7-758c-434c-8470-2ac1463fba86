{% extends "base.html" %}

{% block title %}Accounts - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-users"></i> Account Management
        </h1>
    </div>
</div>

<!-- Active Accounts -->
<div class="row">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-user-check"></i> Active Accounts ({{ active_accounts|length }})</span>
                <a href="{{ url_for('create_account') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i> Create New Account
                </a>
            </div>
            <div class="card-body">
                {% if active_accounts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Account Number</th>
                                    <th>Name</th>
                                    <th>Age</th>
                                    <th>Type</th>
                                    <th>Balance</th>
                                    <th>PIN Set</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in active_accounts %}
                                <tr>
                                    <td><strong>{{ account.account_number }}</strong></td>
                                    <td>{{ account.name }}</td>
                                    <td>{{ account.age }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if account.account_type == 'Savings' else 'info' }}">
                                            {{ account.account_type }}
                                        </span>
                                    </td>
                                    <td>₹{{ "%.2f"|format(account.balance) }}</td>
                                    <td>
                                        {% if account.pin %}
                                            <i class="fas fa-check text-success"></i> Yes
                                        {% else %}
                                            <i class="fas fa-times text-danger"></i> No
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('transaction_history', account_number=account.account_number) }}"
                                               class="btn btn-outline-info" title="Transaction History">
                                                <i class="fas fa-history"></i>
                                            </a>
                                            <a href="{{ url_for('pin_management') }}?account={{ account.account_number }}"
                                               class="btn btn-outline-secondary" title="PIN Management">
                                                <i class="fas fa-lock"></i>
                                            </a>
                                            <button class="btn btn-outline-danger"
                                                    onclick="closeAccount({{ account.account_number }})"
                                                    title="Close Account">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Active Accounts</h5>
                        <p class="text-muted">Create your first account to get started.</p>
                        <a href="{{ url_for('create_account') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Account
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Closed Accounts -->
{% if closed_accounts %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-times"></i> Closed Accounts ({{ closed_accounts|length }})
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Account Number</th>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Type</th>
                                <th>Balance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in closed_accounts %}
                            <tr class="table-secondary">
                                <td><strong>{{ account.account_number }}</strong></td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.age }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ account.account_type }}
                                    </span>
                                </td>
                                <td>₹{{ "%.2f"|format(account.balance) }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('transaction_history', account_number=account.account_number) }}" 
                                           class="btn btn-outline-info" title="Transaction History">
                                            <i class="fas fa-history"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="reopenAccount({{ account.account_number }})" 
                                                title="Reopen Account">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Account Summary -->
<div class="row mt-4">
    <div class="col-md-4 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.1s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="metric-value">{{ active_accounts|length }}</div>
            <div class="metric-label">Active Accounts</div>
            <div class="mt-2">
                <small class="text-success">
                    <i class="fas fa-arrow-up"></i>
                    {{ ((active_accounts|length / (active_accounts|length + closed_accounts|length + 1)) * 100) | round(1) }}% of total
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.2s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="metric-value">{{ closed_accounts|length }}</div>
            <div class="metric-label">Closed Accounts</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    Inactive accounts
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.3s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-value">{{ active_accounts|length + closed_accounts|length }}</div>
            <div class="metric-label">Total Accounts</div>
            <div class="mt-2">
                <small class="text-info">
                    <i class="fas fa-chart-line"></i>
                    All time accounts
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function closeAccount(accountNumber) {
    if (confirm('Are you sure you want to close account ' + accountNumber + '?')) {
        fetch('/close_account/' + accountNumber, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

function reopenAccount(accountNumber) {
    if (confirm('Are you sure you want to reopen account ' + accountNumber + '?')) {
        fetch('/reopen_account/' + accountNumber, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}
</script>
{% endblock %}
