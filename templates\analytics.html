{% extends "base.html" %}

{% block title %}Analytics - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-bar"></i> Account Analytics
        </h1>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ active_count }}</h4>
                <p class="card-text">Active Accounts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-wallet fa-2x text-success mb-2"></i>
                <h4 class="text-success">₹{{ "%.2f"|format(avg_balance) if avg_balance else "0.00" }}</h4>
                <p class="card-text">Average Balance</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-child fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ youngest.age if youngest else 'N/A' }}</h4>
                <p class="card-text">Youngest Customer</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ oldest.age if oldest else 'N/A' }}</h4>
                <p class="card-text">Oldest Customer</p>
            </div>
        </div>
    </div>
</div>

<!-- Customer Demographics -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-friends"></i> Customer Demographics
            </div>
            <div class="card-body">
                {% if youngest %}
                    <div class="mb-3">
                        <h6 class="text-info">Youngest Account Holder</h6>
                        <p><strong>{{ youngest.name }}</strong> ({{ youngest.age }} years)</p>
                        <p class="small text-muted">
                            Account: {{ youngest.account_number }} | 
                            Type: {{ youngest.account_type }} | 
                            Balance: ₹{{ "%.2f"|format(youngest.balance) }}
                        </p>
                    </div>
                {% endif %}
                
                {% if oldest %}
                    <div class="mb-3">
                        <h6 class="text-warning">Oldest Account Holder</h6>
                        <p><strong>{{ oldest.name }}</strong> ({{ oldest.age }} years)</p>
                        <p class="small text-muted">
                            Account: {{ oldest.account_number }} | 
                            Type: {{ oldest.account_type }} | 
                            Balance: ₹{{ "%.2f"|format(oldest.balance) }}
                        </p>
                    </div>
                {% endif %}
                
                <div class="text-center">
                    <p class="text-muted">{{ avg_msg if avg_msg else 'No account data available' }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-trophy"></i> Top Accounts by Balance
            </div>
            <div class="card-body">
                {% if top_accounts %}
                    <div class="list-group list-group-flush">
                        {% for account in top_accounts %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ account.name }}</h6>
                                <small class="text-muted">
                                    Account: {{ account.account_number }} | {{ account.account_type }}
                                </small>
                            </div>
                            <span class="badge bg-success rounded-pill">₹{{ "%.2f"|format(account.balance) }}</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No account data available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Account Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> Account Statistics
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <canvas id="accountTypeChart" width="200" height="200"></canvas>
                            <h6 class="mt-2">Account Types</h6>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <canvas id="accountStatusChart" width="200" height="200"></canvas>
                            <h6 class="mt-2">Account Status</h6>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <canvas id="balanceDistributionChart" width="200" height="200"></canvas>
                            <h6 class="mt-2">Balance Distribution</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> System Information
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-database text-primary"></i> <strong>Data Storage:</strong> CSV Files</li>
                    <li><i class="fas fa-history text-primary"></i> <strong>Transaction Logging:</strong> Enabled</li>
                    <li><i class="fas fa-shield-alt text-primary"></i> <strong>Security:</strong> PIN Protection Available</li>
                    <li><i class="fas fa-clock text-primary"></i> <strong>Processing:</strong> Real-time</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cogs"></i> System Limits
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-money-bill-wave text-success"></i> <strong>Max Single Deposit:</strong> ₹1,00,000</li>
                    <li><i class="fas fa-calendar-day text-info"></i> <strong>Daily Transaction Limit:</strong> ₹50,000</li>
                    <li><i class="fas fa-piggy-bank text-warning"></i> <strong>Global Min Balance:</strong> ₹500</li>
                    <li><i class="fas fa-percentage text-primary"></i> <strong>Interest Rate:</strong> 5% per annum</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list"></i> View All Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('create_account') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-plus"></i> Create Account
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('search') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-search"></i> Search Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary w-100" onclick="refreshAnalytics()">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sample data for charts (in a real application, this would come from the backend)
const accountTypeData = {
    labels: ['Savings', 'Current'],
    datasets: [{
        data: [60, 40], // This would be dynamic
        backgroundColor: ['#007bff', '#17a2b8'],
        borderWidth: 2
    }]
};

const accountStatusData = {
    labels: ['Active', 'Inactive'],
    datasets: [{
        data: [{{ active_count }}, 10], // This would be dynamic
        backgroundColor: ['#28a745', '#6c757d'],
        borderWidth: 2
    }]
};

const balanceDistributionData = {
    labels: ['< ₹1,000', '₹1,000 - ₹10,000', '₹10,000 - ₹50,000', '> ₹50,000'],
    datasets: [{
        data: [20, 40, 30, 10], // This would be dynamic
        backgroundColor: ['#dc3545', '#ffc107', '#28a745', '#007bff'],
        borderWidth: 2
    }]
};

// Chart configuration
const chartConfig = {
    type: 'doughnut',
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 10,
                    usePointStyle: true
                }
            }
        }
    }
};

// Create charts
new Chart(document.getElementById('accountTypeChart'), {
    ...chartConfig,
    data: accountTypeData
});

new Chart(document.getElementById('accountStatusChart'), {
    ...chartConfig,
    data: accountStatusData
});

new Chart(document.getElementById('balanceDistributionChart'), {
    ...chartConfig,
    data: balanceDistributionData
});

function refreshAnalytics() {
    location.reload();
}

// Add some animation to the metric cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
