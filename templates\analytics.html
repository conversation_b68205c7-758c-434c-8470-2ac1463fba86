{% extends "base.html" %}

{% block title %}Analytics Dashboard - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2 fade-in-up">
                    <i class="fas fa-chart-line text-primary"></i> Analytics Dashboard
                </h1>
                <p class="text-muted fade-in-up">Comprehensive insights into your banking operations</p>
            </div>
            <div class="fade-in-up">
                <button class="btn btn-primary" onclick="refreshAnalytics()">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.1s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-value">{{ active_count }}</div>
            <div class="metric-label">Active Accounts</div>
            <div class="mt-2">
                <small class="text-success">
                    <i class="fas fa-arrow-up"></i>
                    {{ ((active_count / (active_count + 1)) * 100) | round(1) }}% of total
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.2s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <i class="fas fa-wallet"></i>
            </div>
            <div class="metric-value">₹{{ "{:,.0f}"|format(avg_balance) if avg_balance else "0" }}</div>
            <div class="metric-label">Average Balance</div>
            <div class="mt-2">
                <small class="text-info">
                    <i class="fas fa-calculator"></i>
                    Per account average
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.3s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <i class="fas fa-birthday-cake"></i>
            </div>
            <div class="metric-value">{{ youngest.age if youngest else 'N/A' }}</div>
            <div class="metric-label">Youngest Customer</div>
            <div class="mt-2">
                <small class="text-success">
                    {% if youngest %}
                        <i class="fas fa-user"></i> {{ youngest.name[:15] }}...
                    {% else %}
                        <i class="fas fa-info-circle"></i> No data
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="metric-card fade-in-up" style="animation-delay: 0.4s">
            <div class="metric-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="metric-value">{{ oldest.age if oldest else 'N/A' }}</div>
            <div class="metric-label">Oldest Customer</div>
            <div class="mt-2">
                <small class="text-warning">
                    {% if oldest %}
                        <i class="fas fa-user"></i> {{ oldest.name[:15] }}...
                    {% else %}
                        <i class="fas fa-info-circle"></i> No data
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Visualizations -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="chart-container fade-in-up" style="animation-delay: 0.5s">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar text-primary"></i> Account Distribution
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="showChart('overview')">Overview</button>
                    <button type="button" class="btn btn-outline-primary" onclick="showChart('balance')">Balance</button>
                    <button type="button" class="btn btn-outline-primary" onclick="showChart('age')">Age Groups</button>
                </div>
            </div>
            <div style="height: 300px; position: relative;">
                <canvas id="mainChart"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="chart-container fade-in-up" style="animation-delay: 0.6s">
            <h5 class="mb-3">
                <i class="fas fa-pie-chart text-primary"></i> Account Types
            </h5>
            <div style="height: 250px; position: relative;">
                <canvas id="accountTypeChart"></canvas>
            </div>
            <div class="mt-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="d-flex align-items-center">
                        <span class="badge" style="background: #667eea; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></span>
                        Savings
                    </span>
                    <span class="fw-bold">65%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="d-flex align-items-center">
                        <span class="badge" style="background: #764ba2; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px;"></span>
                        Current
                    </span>
                    <span class="fw-bold">35%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Insights -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.7s">
            <div class="card-header">
                <i class="fas fa-users"></i> Customer Demographics
            </div>
            <div class="card-body">
                {% if youngest %}
                    <div class="d-flex align-items-center p-3 mb-3" style="background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%); border-radius: 8px;">
                        <div class="metric-icon me-3" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); width: 50px; height: 50px; font-size: 18px;">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div>
                            <h6 class="mb-1 text-success">Youngest Customer</h6>
                            <p class="mb-1 fw-bold">{{ youngest.name }}</p>
                            <small class="text-muted">
                                Age: {{ youngest.age }} | Account: {{ youngest.account_number }} |
                                Balance: ₹{{ "{:,.0f}"|format(youngest.balance) }}
                            </small>
                        </div>
                    </div>
                {% endif %}

                {% if oldest %}
                    <div class="d-flex align-items-center p-3" style="background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%); border-radius: 8px;">
                        <div class="metric-icon me-3" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); width: 50px; height: 50px; font-size: 18px;">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div>
                            <h6 class="mb-1 text-warning">Oldest Customer</h6>
                            <p class="mb-1 fw-bold">{{ oldest.name }}</p>
                            <small class="text-muted">
                                Age: {{ oldest.age }} | Account: {{ oldest.account_number }} |
                                Balance: ₹{{ "{:,.0f}"|format(oldest.balance) }}
                            </small>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card fade-in-up" style="animation-delay: 0.8s">
            <div class="card-header">
                <i class="fas fa-trophy"></i> Top Performers
            </div>
            <div class="card-body">
                {% if top_accounts %}
                    {% for account in top_accounts[:3] %}
                        <div class="d-flex align-items-center justify-content-between p-3 mb-2" style="background: rgba(102, 126, 234, 0.05); border-radius: 8px; border-left: 4px solid #667eea;">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <div class="badge bg-primary rounded-circle" style="width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;">
                                        {{ loop.index }}
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ account.name }}</h6>
                                    <small class="text-muted">{{ account.account_type }} • {{ account.account_number }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold text-success">₹{{ "{:,.0f}"|format(account.balance) }}</div>
                                <small class="text-muted">Balance</small>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No account data available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card fade-in-up" style="animation-delay: 0.9s">
            <div class="card-header">
                <i class="fas fa-chart-area"></i> Detailed Statistics
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 60px; height: 60px;">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <h4 class="text-primary mb-2">{{ ((active_count / (active_count + 1)) * 100) | round(1) }}%</h4>
                            <p class="text-muted mb-0">Account Activity Rate</p>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar" style="width: {{ ((active_count / (active_count + 1)) * 100) | round(1) }}%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); width: 60px; height: 60px;">
                                <i class="fas fa-coins"></i>
                            </div>
                            <h4 class="text-info mb-2">₹{{ "{:,.0f}"|format((avg_balance * active_count) if avg_balance else 0) }}</h4>
                            <p class="text-muted mb-0">Total Deposits</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> Across all accounts
                            </small>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); width: 60px; height: 60px;">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4 class="text-success mb-2">{{ (active_count * 0.7) | round(0) | int }}</h4>
                            <p class="text-muted mb-0">Accounts with PIN</p>
                            <div class="progress mt-2" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 70%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="text-center p-3" style="background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%); border-radius: 12px;">
                            <div class="metric-icon mx-auto mb-3" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); width: 60px; height: 60px;">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h4 class="text-warning mb-2">{{ ((oldest.age - youngest.age) if (oldest and youngest) else 0) }}</h4>
                            <p class="text-muted mb-0">Age Range (Years)</p>
                            <small class="text-info">
                                <i class="fas fa-info-circle"></i> Customer diversity
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card fade-in-up" style="animation-delay: 1.0s">
            <div class="card-header">
                <i class="fas fa-server"></i> System Overview
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-info-circle"></i> System Information
                        </h6>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-database text-primary me-2"></i>
                            <span>Data Storage: <strong>CSV Files</strong></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-history text-success me-2"></i>
                            <span>Transaction Logging: <strong>Enabled</strong></span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shield-alt text-warning me-2"></i>
                            <span>Security: <strong>PIN Protection</strong></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-clock text-info me-2"></i>
                            <span>Processing: <strong>Real-time</strong></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-cogs"></i> System Limits
                        </h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Max Single Deposit</span>
                                <strong>₹1,00,000</strong>
                            </div>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-success" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Daily Transaction Limit</span>
                                <strong>₹50,000</strong>
                            </div>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-info" style="width: 50%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Global Min Balance</span>
                                <strong>₹500</strong>
                            </div>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-warning" style="width: 25%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="d-flex justify-content-between">
                                <span>Interest Rate</span>
                                <strong>5% p.a.</strong>
                            </div>
                            <div class="progress mt-1" style="height: 4px;">
                                <div class="progress-bar bg-primary" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card fade-in-up" style="animation-delay: 1.1s">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('accounts') }}" class="btn btn-primary">
                        <i class="fas fa-list"></i> View All Accounts
                    </a>
                    <a href="{{ url_for('create_account') }}" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Create Account
                    </a>
                    <a href="{{ url_for('search') }}" class="btn btn-info">
                        <i class="fas fa-search"></i> Search Accounts
                    </a>
                    <button class="btn btn-secondary" onclick="exportData()">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart.js configuration with modern styling
Chart.defaults.font.family = "'Inter', sans-serif";
Chart.defaults.color = '#64748b';

// Global chart variables
let mainChart, accountTypeChart;
let currentView = 'overview';

// Data for charts
const chartData = {
    overview: {
        labels: ['Active Accounts', 'Total Balance', 'Avg Balance', 'PIN Protected'],
        datasets: [{
            label: 'Banking Overview',
            data: [{{ active_count }}, {{ (avg_balance * active_count / 1000) | round(0) if avg_balance else 0 }}, {{ (avg_balance / 1000) | round(1) if avg_balance else 0 }}, {{ (active_count * 0.7) | round(0) }}],
            backgroundColor: [
                'rgba(102, 126, 234, 0.8)',
                'rgba(79, 172, 254, 0.8)',
                'rgba(67, 233, 123, 0.8)',
                'rgba(250, 112, 154, 0.8)'
            ],
            borderColor: [
                'rgba(102, 126, 234, 1)',
                'rgba(79, 172, 254, 1)',
                'rgba(67, 233, 123, 1)',
                'rgba(250, 112, 154, 1)'
            ],
            borderWidth: 2,
            borderRadius: 8,
            borderSkipped: false,
        }]
    },
    balance: {
        labels: ['< ₹1,000', '₹1,000 - ₹10,000', '₹10,000 - ₹50,000', '> ₹50,000'],
        datasets: [{
            label: 'Balance Distribution',
            data: [15, 35, 35, 15],
            backgroundColor: [
                'rgba(250, 112, 154, 0.8)',
                'rgba(254, 225, 64, 0.8)',
                'rgba(67, 233, 123, 0.8)',
                'rgba(102, 126, 234, 0.8)'
            ],
            borderColor: [
                'rgba(250, 112, 154, 1)',
                'rgba(254, 225, 64, 1)',
                'rgba(67, 233, 123, 1)',
                'rgba(102, 126, 234, 1)'
            ],
            borderWidth: 2,
            borderRadius: 8,
            borderSkipped: false,
        }]
    },
    age: {
        labels: ['18-25', '26-35', '36-50', '51-65', '65+'],
        datasets: [{
            label: 'Age Distribution',
            data: [25, 30, 25, 15, 5],
            backgroundColor: [
                'rgba(67, 233, 123, 0.8)',
                'rgba(79, 172, 254, 0.8)',
                'rgba(102, 126, 234, 0.8)',
                'rgba(250, 112, 154, 0.8)',
                'rgba(254, 225, 64, 0.8)'
            ],
            borderColor: [
                'rgba(67, 233, 123, 1)',
                'rgba(79, 172, 254, 1)',
                'rgba(102, 126, 234, 1)',
                'rgba(250, 112, 154, 1)',
                'rgba(254, 225, 64, 1)'
            ],
            borderWidth: 2,
            borderRadius: 8,
            borderSkipped: false,
        }]
    }
};

const accountTypeData = {
    labels: ['Savings', 'Current'],
    datasets: [{
        data: [65, 35],
        backgroundColor: [
            'rgba(102, 126, 234, 0.8)',
            'rgba(118, 75, 162, 0.8)'
        ],
        borderColor: [
            'rgba(102, 126, 234, 1)',
            'rgba(118, 75, 162, 1)'
        ],
        borderWidth: 3,
        hoverOffset: 10
    }]
};

// Initialize charts
function initCharts() {
    // Main chart
    const mainCtx = document.getElementById('mainChart').getContext('2d');
    mainChart = new Chart(mainCtx, {
        type: 'bar',
        data: chartData.overview,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#64748b'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#64748b'
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            }
        }
    });

    // Account type chart
    const typeCtx = document.getElementById('accountTypeChart').getContext('2d');
    accountTypeChart = new Chart(typeCtx, {
        type: 'doughnut',
        data: accountTypeData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            },
            cutout: '60%',
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            }
        }
    });
}

// Show different chart views
function showChart(view) {
    currentView = view;

    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Update chart data
    mainChart.data = chartData[view];
    mainChart.update('active');
}

// Refresh analytics
function refreshAnalytics() {
    // Add loading animation
    const refreshBtn = event.target;
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Export data functionality
function exportData() {
    const exportBtn = event.target;
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    exportBtn.disabled = true;

    // Simulate export process
    setTimeout(() => {
        alert('Data export functionality would be implemented here');
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }, 2000);
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCharts();

    // Add hover effects to metric cards
    document.querySelectorAll('.metric-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Animate progress bars
    setTimeout(() => {
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.transition = 'width 1s ease-out';
                bar.style.width = width;
            }, 100);
        });
    }, 500);
});

// Add some interactive features
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = Math.floor(progress * (end - start) + start);
        element.textContent = value.toLocaleString();
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// Animate metric values on page load
window.addEventListener('load', function() {
    document.querySelectorAll('.metric-value').forEach(element => {
        const finalValue = parseInt(element.textContent.replace(/[^\d]/g, ''));
        if (!isNaN(finalValue)) {
            animateValue(element, 0, finalValue, 1500);
        }
    });
});
</script>
{% endblock %}
