from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from services.banking_service import BankingService

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this in production

# Initialize the banking service
try:
    bank = BankingService()
    print("Banking service initialized successfully")
except Exception as e:
    print(f"Error initializing banking service: {e}")
    bank = None

@app.route('/')
def index():
    """Main dashboard page"""
    if not bank:
        flash('Banking service not available', 'error')
    return render_template('index.html')

@app.route('/accounts')
def accounts():
    """View all accounts"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('accounts.html', active_accounts=[], closed_accounts=[])

    try:
        active_accounts, _ = bank.list_active_accounts()
        closed_accounts, _ = bank.list_closed_accounts()
        return render_template('accounts.html',
                             active_accounts=active_accounts,
                             closed_accounts=closed_accounts)
    except Exception as e:
        flash(f'Error loading accounts: {str(e)}', 'error')
        return render_template('accounts.html', active_accounts=[], closed_accounts=[])

@app.route('/create_account', methods=['GET', 'POST'])
def create_account():
    """Create a new account"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('create_account.html')

    if request.method == 'POST':
        try:
            name = request.form['name']
            age = request.form['age']
            account_type = request.form['account_type']
            initial_deposit = request.form['initial_deposit']

            account, message = bank.create_account(name, age, account_type, initial_deposit)

            if account:
                flash(f'Account created successfully! Account Number: {account.account_number}', 'success')
                return redirect(url_for('accounts'))
            else:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error creating account: {str(e)}', 'error')

    return render_template('create_account.html')

@app.route('/deposit', methods=['GET', 'POST'])
def deposit():
    """Deposit money to an account"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('deposit.html')

    if request.method == 'POST':
        try:
            account_number = request.form['account_number']
            amount = request.form['amount']

            success, message = bank.deposit(account_number, amount)

            if success:
                flash(message, 'success')
            else:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error processing deposit: {str(e)}', 'error')

        return redirect(url_for('deposit'))

    return render_template('deposit.html')

@app.route('/withdraw', methods=['GET', 'POST'])
def withdraw():
    """Withdraw money from an account"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('withdraw.html')

    if request.method == 'POST':
        try:
            account_number = request.form['account_number']
            amount = request.form['amount']

            success, message = bank.withdraw(account_number, amount)

            if success:
                flash(message, 'success')
            else:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error processing withdrawal: {str(e)}', 'error')

        return redirect(url_for('withdraw'))

    return render_template('withdraw.html')

@app.route('/balance_inquiry', methods=['GET', 'POST'])
def balance_inquiry():
    """Check account balance"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('balance_inquiry.html', account=None, message=None)

    account = None
    message = None

    if request.method == 'POST':
        try:
            account_number = request.form['account_number']
            account, message = bank.balance_inquiry(account_number)

            if not account:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error checking balance: {str(e)}', 'error')

    return render_template('balance_inquiry.html', account=account, message=message)

@app.route('/transfer', methods=['GET', 'POST'])
def transfer():
    """Transfer funds between accounts"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('transfer.html')

    if request.method == 'POST':
        try:
            from_account = request.form['from_account']
            to_account = request.form['to_account']
            amount = request.form['amount']

            success, message = bank.transfer_funds(from_account, to_account, amount)

            if success:
                flash(message, 'success')
            else:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error processing transfer: {str(e)}', 'error')

        return redirect(url_for('transfer'))

    return render_template('transfer.html')

@app.route('/search', methods=['GET', 'POST'])
def search():
    """Search for accounts"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('search.html', results=[], search_type=None)

    results = []
    search_type = None

    if request.method == 'POST':
        try:
            search_type = request.form['search_type']
            search_value = request.form['search_value']

            if search_type == 'name':
                results, message = bank.search_by_name(search_value)
            elif search_type == 'account_number':
                account, message = bank.search_by_account_number(search_value)
                results = [account] if account else []

            if not results:
                flash(message, 'error')
        except Exception as e:
            flash(f'Error searching accounts: {str(e)}', 'error')

    return render_template('search.html', results=results, search_type=search_type)

@app.route('/transaction_history/<int:account_number>')
def transaction_history(account_number):
    """View transaction history for an account"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('transaction_history.html', transactions=[], account=None, message="Service unavailable")

    try:
        transactions, message = bank.get_transaction_history(account_number)
        account = bank.get_account(account_number)

        return render_template('transaction_history.html',
                             transactions=transactions,
                             account=account,
                             message=message)
    except Exception as e:
        flash(f'Error loading transaction history: {str(e)}', 'error')
        return render_template('transaction_history.html', transactions=[], account=None, message=str(e))

@app.route('/analytics')
def analytics():
    """View account analytics"""
    if not bank:
        flash('Banking service not available', 'error')
        return render_template('analytics.html',
                             avg_balance=0, avg_msg="Service unavailable",
                             active_count=0, count_msg="Service unavailable",
                             youngest=None, youngest_msg="Service unavailable",
                             oldest=None, oldest_msg="Service unavailable",
                             top_accounts=[], top_msg="Service unavailable")

    try:
        # Get various analytics
        avg_balance, avg_msg = bank.calculate_average_balance()
        active_count, count_msg = bank.count_active_accounts()
        youngest, youngest_msg = bank.get_youngest_account_holder()
        oldest, oldest_msg = bank.get_oldest_account_holder()
        top_accounts, top_msg = bank.get_top_accounts_by_balance(5)

        return render_template('analytics.html',
                             avg_balance=avg_balance,
                             avg_msg=avg_msg,
                             active_count=active_count,
                             count_msg=count_msg,
                             youngest=youngest,
                             youngest_msg=youngest_msg,
                             oldest=oldest,
                             oldest_msg=oldest_msg,
                             top_accounts=top_accounts,
                             top_msg=top_msg)
    except Exception as e:
        flash(f'Error loading analytics: {str(e)}', 'error')
        return render_template('analytics.html',
                             avg_balance=0, avg_msg=str(e),
                             active_count=0, count_msg=str(e),
                             youngest=None, youngest_msg=str(e),
                             oldest=None, oldest_msg=str(e),
                             top_accounts=[], top_msg=str(e))

@app.route('/api/account/<int:account_number>')
def api_get_account(account_number):
    """API endpoint to get account details"""
    if not bank:
        return jsonify({'error': 'Banking service not available'}), 503

    try:
        account = bank.get_account(account_number)
        if account:
            return jsonify(account.to_dict())
        else:
            return jsonify({'error': 'Account not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/close_account/<int:account_number>', methods=['POST'])
def close_account(account_number):
    """Close an account"""
    if not bank:
        flash('Banking service not available', 'error')
        return redirect(url_for('accounts'))

    try:
        success, message = bank.close_account(account_number)

        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')
    except Exception as e:
        flash(f'Error closing account: {str(e)}', 'error')

    return redirect(url_for('accounts'))

@app.route('/reopen_account/<int:account_number>', methods=['POST'])
def reopen_account(account_number):
    """Reopen a closed account"""
    if not bank:
        flash('Banking service not available', 'error')
        return redirect(url_for('accounts'))

    try:
        success, message = bank.reopen_account(account_number)

        if success:
            flash(message, 'success')
        else:
            flash(message, 'error')
    except Exception as e:
        flash(f'Error reopening account: {str(e)}', 'error')

    return redirect(url_for('accounts'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
