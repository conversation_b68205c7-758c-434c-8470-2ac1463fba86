{% extends "base.html" %}

{% block title %}Balance Inquiry - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search-dollar"></i> Balance Inquiry
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-wallet"></i> Check Account Balance
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="account_number" class="form-label">
                            <i class="fas fa-hashtag"></i> Account Number *
                        </label>
                        <input type="number" class="form-control" id="account_number" name="account_number" 
                               value="{{ request.form.account_number if request.form.account_number else '' }}" required>
                        <div class="form-text">Enter the account number to check balance</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search"></i> Check Balance
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Balance Information -->
{% if account %}
<div class="row justify-content-center mt-4">
    <div class="col-md-8 col-lg-6">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <i class="fas fa-check-circle"></i> Account Information
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Account Details</h6>
                        <p><strong>Account Number:</strong> {{ account.account_number }}</p>
                        <p><strong>Account Holder:</strong> {{ account.name }}</p>
                        <p><strong>Account Type:</strong> 
                            <span class="badge bg-{{ 'primary' if account.account_type == 'Savings' else 'info' }}">
                                {{ account.account_type }}
                            </span>
                        </p>
                        <p><strong>Age:</strong> {{ account.age }} years</p>
                        <p><strong>Status:</strong> 
                            <span class="badge bg-{{ 'success' if account.status == 'Active' else 'secondary' }}">
                                {{ account.status }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Balance Information</h6>
                        <div class="text-center">
                            <h2 class="text-success">₹{{ "%.2f"|format(account.balance) }}</h2>
                            <p class="text-muted">Current Balance</p>
                        </div>
                        <hr>
                        <p><strong>PIN Set:</strong> 
                            {% if account.pin %}
                                <i class="fas fa-check text-success"></i> Yes
                            {% else %}
                                <i class="fas fa-times text-danger"></i> No
                            {% endif %}
                        </p>
                        <p><strong>Minimum Balance:</strong> 
                            ₹{{ 500 if account.account_type == 'Savings' else 1000 }}
                        </p>
                    </div>
                </div>
                
                <!-- Quick Actions for this account -->
                <hr>
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('deposit') }}" class="btn btn-success btn-sm w-100">
                            <i class="fas fa-plus-circle"></i> Deposit
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('withdraw') }}" class="btn btn-warning btn-sm w-100">
                            <i class="fas fa-minus-circle"></i> Withdraw
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('transaction_history', account_number=account.account_number) }}"
                           class="btn btn-info btn-sm w-100">
                            <i class="fas fa-history"></i> History
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('pin_management') }}?account={{ account.account_number }}"
                           class="btn btn-secondary btn-sm w-100">
                            <i class="fas fa-lock"></i> PIN
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Information Cards -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Balance Inquiry Features
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Real-time balance information</li>
                    <li><i class="fas fa-check text-success"></i> Account status verification</li>
                    <li><i class="fas fa-check text-success"></i> Complete account details</li>
                    <li><i class="fas fa-check text-success"></i> Quick action buttons</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security Information
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-lock text-primary"></i> Secure data access</li>
                    <li><i class="fas fa-eye text-primary"></i> View-only operation</li>
                    <li><i class="fas fa-user-shield text-primary"></i> Account verification</li>
                    <li><i class="fas fa-history text-primary"></i> Access logging</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Quick Search -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-search"></i> Other Search Options
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('search') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-search"></i> Search by Name
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list"></i> View All Accounts
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('analytics') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-bar"></i> Account Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-fill account number from URL parameter if present
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const accountNumber = urlParams.get('account');
    if (accountNumber) {
        document.getElementById('account_number').value = accountNumber;
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const accountNumber = document.getElementById('account_number').value;
    
    if (!accountNumber || accountNumber.trim() === '') {
        e.preventDefault();
        alert('Please enter an account number');
        return;
    }
    
    if (isNaN(accountNumber) || parseInt(accountNumber) <= 0) {
        e.preventDefault();
        alert('Please enter a valid account number');
        return;
    }
});

// Auto-focus on account number input
document.getElementById('account_number').focus();
</script>
{% endblock %}
