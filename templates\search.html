{% extends "base.html" %}

{% block title %}Search Accounts - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search"></i> Search Accounts
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-search"></i> Account Search
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="search_type" class="form-label">
                            <i class="fas fa-filter"></i> Search Type *
                        </label>
                        <select class="form-select" id="search_type" name="search_type" required onchange="updateSearchPlaceholder()">
                            <option value="">Select Search Type</option>
                            <option value="name" {{ 'selected' if search_type == 'name' }}>Search by Name</option>
                            <option value="account_number" {{ 'selected' if search_type == 'account_number' }}>Search by Account Number</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="search_value" class="form-label">
                            <i class="fas fa-keyboard"></i> Search Value *
                        </label>
                        <input type="text" class="form-control" id="search_value" name="search_value" 
                               value="{{ request.form.search_value if request.form.search_value else '' }}" 
                               placeholder="Enter search value" required>
                        <div class="form-text" id="search-help">Enter the value to search for</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search"></i> Search Accounts
                        </button>
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> View All Accounts
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
{% if results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-search-plus"></i> Search Results ({{ results|length }} found)</span>
                <span class="badge bg-primary">{{ search_type.replace('_', ' ').title() }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Account Number</th>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Type</th>
                                <th>Balance</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in results %}
                            <tr>
                                <td><strong>{{ account.account_number }}</strong></td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.age }}</td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if account.account_type == 'Savings' else 'info' }}">
                                        {{ account.account_type }}
                                    </span>
                                </td>
                                <td>₹{{ "%.2f"|format(account.balance) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if account.status == 'Active' else 'secondary' }}">
                                        {{ account.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('balance_inquiry') }}?account={{ account.account_number }}" 
                                           class="btn btn-outline-primary" title="Balance Inquiry">
                                            <i class="fas fa-search-dollar"></i>
                                        </a>
                                        <a href="{{ url_for('transaction_history', account_number=account.account_number) }}" 
                                           class="btn btn-outline-info" title="Transaction History">
                                            <i class="fas fa-history"></i>
                                        </a>
                                        {% if account.status == 'Active' %}
                                            <button class="btn btn-outline-danger" 
                                                    onclick="closeAccount({{ account.account_number }})" 
                                                    title="Close Account">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="reopenAccount({{ account.account_number }})" 
                                                    title="Reopen Account">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Tips -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-lightbulb"></i> Search Tips
            </div>
            <div class="card-body">
                <h6>Name Search:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> Partial names are supported</li>
                    <li><i class="fas fa-check text-success"></i> Case-insensitive search</li>
                    <li><i class="fas fa-check text-success"></i> Returns all matching accounts</li>
                </ul>
                
                <h6 class="mt-3">Account Number Search:</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> Exact match required</li>
                    <li><i class="fas fa-check text-success"></i> Returns single account</li>
                    <li><i class="fas fa-check text-success"></i> Numeric input only</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> View All Accounts
                    </a>
                    <a href="{{ url_for('create_account') }}" class="btn btn-outline-success">
                        <i class="fas fa-user-plus"></i> Create New Account
                    </a>
                    <a href="{{ url_for('analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> View Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Common Searches -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-star"></i> Common Searches
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary w-100" onclick="quickSearch('name', 'A')">
                            <i class="fas fa-user"></i> Names starting with 'A'
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary w-100" onclick="quickSearch('name', 'S')">
                            <i class="fas fa-user"></i> Names starting with 'S'
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-users"></i> All Active Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('analytics') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-chart-line"></i> Account Statistics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateSearchPlaceholder() {
    const searchType = document.getElementById('search_type').value;
    const searchValue = document.getElementById('search_value');
    const searchHelp = document.getElementById('search-help');
    
    if (searchType === 'name') {
        searchValue.placeholder = 'Enter name or partial name';
        searchValue.type = 'text';
        searchHelp.innerHTML = 'Enter full name or partial name (case-insensitive)';
    } else if (searchType === 'account_number') {
        searchValue.placeholder = 'Enter account number';
        searchValue.type = 'number';
        searchHelp.innerHTML = 'Enter exact account number';
    } else {
        searchValue.placeholder = 'Enter search value';
        searchValue.type = 'text';
        searchHelp.innerHTML = 'Select search type first';
    }
}

function quickSearch(type, value) {
    document.getElementById('search_type').value = type;
    document.getElementById('search_value').value = value;
    updateSearchPlaceholder();
    document.querySelector('form').submit();
}

function closeAccount(accountNumber) {
    if (confirm('Are you sure you want to close account ' + accountNumber + '?')) {
        fetch('/close_account/' + accountNumber, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

function reopenAccount(accountNumber) {
    if (confirm('Are you sure you want to reopen account ' + accountNumber + '?')) {
        fetch('/reopen_account/' + accountNumber, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            }
        });
    }
}

// Initialize placeholder on page load
document.addEventListener('DOMContentLoaded', function() {
    updateSearchPlaceholder();
});
</script>
{% endblock %}
