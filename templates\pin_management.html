{% extends "base.html" %}

{% block title %}PIN Management - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-lock"></i> PIN Management
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-key"></i> Account PIN Operations
            </div>
            <div class="card-body">
                <!-- Account Selection -->
                <div class="mb-4">
                    <label for="account_number" class="form-label">
                        <i class="fas fa-hashtag"></i> Account Number *
                    </label>
                    <input type="number" class="form-control" id="account_number" required>
                    <div class="form-text">Enter the account number for PIN operations</div>
                    <div id="account-info" class="mt-2"></div>
                </div>

                <!-- PIN Operations Tabs -->
                <ul class="nav nav-tabs" id="pinTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="set-pin-tab" data-bs-toggle="tab" data-bs-target="#set-pin" type="button" role="tab">
                            <i class="fas fa-plus-circle"></i> Set PIN
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="change-pin-tab" data-bs-toggle="tab" data-bs-target="#change-pin" type="button" role="tab">
                            <i class="fas fa-edit"></i> Change PIN
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="verify-pin-tab" data-bs-toggle="tab" data-bs-target="#verify-pin" type="button" role="tab">
                            <i class="fas fa-check-circle"></i> Verify PIN
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="pinTabContent">
                    <!-- Set PIN Tab -->
                    <div class="tab-pane fade show active" id="set-pin" role="tabpanel">
                        <form method="POST" id="setPinForm">
                            <input type="hidden" name="action" value="set_pin">
                            <input type="hidden" name="account_number" id="set_account_number">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_pin_set" class="form-label">
                                            <i class="fas fa-key"></i> New PIN *
                                        </label>
                                        <input type="password" class="form-control" id="new_pin_set" name="new_pin" 
                                               pattern="[0-9]{4}" maxlength="4" required>
                                        <div class="form-text">Enter 4-digit PIN</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_pin_set" class="form-label">
                                            <i class="fas fa-key"></i> Confirm PIN *
                                        </label>
                                        <input type="password" class="form-control" id="confirm_pin_set" name="confirm_pin" 
                                               pattern="[0-9]{4}" maxlength="4" required>
                                        <div class="form-text">Re-enter the same PIN</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success" id="setPinBtn" disabled>
                                    <i class="fas fa-plus-circle"></i> Set PIN
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Change PIN Tab -->
                    <div class="tab-pane fade" id="change-pin" role="tabpanel">
                        <form method="POST" id="changePinForm">
                            <input type="hidden" name="action" value="change_pin">
                            <input type="hidden" name="account_number" id="change_account_number">
                            
                            <div class="mb-3">
                                <label for="old_pin" class="form-label">
                                    <i class="fas fa-key"></i> Current PIN *
                                </label>
                                <input type="password" class="form-control" id="old_pin" name="old_pin" 
                                       pattern="[0-9]{4}" maxlength="4" required>
                                <div class="form-text">Enter your current PIN</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_pin_change" class="form-label">
                                            <i class="fas fa-key"></i> New PIN *
                                        </label>
                                        <input type="password" class="form-control" id="new_pin_change" name="new_pin" 
                                               pattern="[0-9]{4}" maxlength="4" required>
                                        <div class="form-text">Enter new 4-digit PIN</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="confirm_pin_change" class="form-label">
                                            <i class="fas fa-key"></i> Confirm New PIN *
                                        </label>
                                        <input type="password" class="form-control" id="confirm_pin_change" name="confirm_pin" 
                                               pattern="[0-9]{4}" maxlength="4" required>
                                        <div class="form-text">Re-enter the new PIN</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning" id="changePinBtn" disabled>
                                    <i class="fas fa-edit"></i> Change PIN
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Verify PIN Tab -->
                    <div class="tab-pane fade" id="verify-pin" role="tabpanel">
                        <form method="POST" id="verifyPinForm">
                            <input type="hidden" name="action" value="verify_pin">
                            <input type="hidden" name="account_number" id="verify_account_number">
                            
                            <div class="mb-3">
                                <label for="pin_verify" class="form-label">
                                    <i class="fas fa-key"></i> Enter PIN *
                                </label>
                                <input type="password" class="form-control" id="pin_verify" name="pin" 
                                       pattern="[0-9]{4}" maxlength="4" required>
                                <div class="form-text">Enter your 4-digit PIN to verify</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-info" id="verifyPinBtn" disabled>
                                    <i class="fas fa-check-circle"></i> Verify PIN
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PIN Security Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> PIN Security Guidelines
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> PIN must be exactly 4 digits</li>
                    <li><i class="fas fa-check text-success"></i> Use numbers only (0-9)</li>
                    <li><i class="fas fa-check text-success"></i> Avoid obvious combinations (1234, 0000)</li>
                    <li><i class="fas fa-check text-success"></i> Keep your PIN confidential</li>
                    <li><i class="fas fa-check text-success"></i> Change PIN regularly for security</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> PIN Operations
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-success">Set PIN</h6>
                    <p class="small">Create a new PIN for accounts without one</p>
                </div>
                <div class="mb-3">
                    <h6 class="text-warning">Change PIN</h6>
                    <p class="small">Update existing PIN (requires current PIN)</p>
                </div>
                <div class="mb-3">
                    <h6 class="text-info">Verify PIN</h6>
                    <p class="small">Check if entered PIN is correct</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list"></i> View Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('search') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-search"></i> Search Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('balance_inquiry') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-search-dollar"></i> Balance Inquiry
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentAccountData = null;

// Account number validation and info fetching
document.getElementById('account_number').addEventListener('blur', function() {
    const accountNumber = this.value;
    const accountInfo = document.getElementById('account-info');
    
    if (accountNumber) {
        fetch('/api/account/' + accountNumber + '/pin_status')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle"></i> ' + data.error + '</div>';
                    currentAccountData = null;
                } else if (data.status !== 'Active') {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-ban"></i> Account is not active</div>';
                    currentAccountData = null;
                } else {
                    currentAccountData = data;
                    const pinStatus = data.has_pin ? 
                        '<span class="badge bg-success"><i class="fas fa-check"></i> PIN Set</span>' : 
                        '<span class="badge bg-warning"><i class="fas fa-times"></i> No PIN</span>';
                    
                    accountInfo.innerHTML = `
                        <div class="alert alert-info alert-sm">
                            <i class="fas fa-user"></i> <strong>${data.name}</strong><br>
                            <i class="fas fa-hashtag"></i> Account: ${data.account_number}<br>
                            <i class="fas fa-lock"></i> PIN Status: ${pinStatus}
                        </div>
                    `;
                    
                    // Update tab visibility based on PIN status
                    updateTabVisibility(data.has_pin);
                }
                updateFormStates();
            })
            .catch(error => {
                accountInfo.innerHTML = '<div class="alert alert-warning alert-sm"><i class="fas fa-exclamation-triangle"></i> Unable to fetch account information</div>';
                currentAccountData = null;
                updateFormStates();
            });
    } else {
        accountInfo.innerHTML = '';
        currentAccountData = null;
        updateFormStates();
    }
});

function updateTabVisibility(hasPin) {
    const setPinTab = document.getElementById('set-pin-tab');
    const changePinTab = document.getElementById('change-pin-tab');
    const verifyPinTab = document.getElementById('verify-pin-tab');
    
    if (hasPin) {
        // If PIN exists, show change and verify tabs, hide set tab
        setPinTab.style.display = 'none';
        changePinTab.style.display = 'block';
        verifyPinTab.style.display = 'block';
        
        // Switch to change PIN tab
        changePinTab.click();
    } else {
        // If no PIN, show set tab, hide others
        setPinTab.style.display = 'block';
        changePinTab.style.display = 'none';
        verifyPinTab.style.display = 'none';
        
        // Switch to set PIN tab
        setPinTab.click();
    }
}

function updateFormStates() {
    const accountNumber = document.getElementById('account_number').value;
    const isValid = currentAccountData && accountNumber;
    
    // Update hidden account number fields
    document.getElementById('set_account_number').value = accountNumber;
    document.getElementById('change_account_number').value = accountNumber;
    document.getElementById('verify_account_number').value = accountNumber;
    
    // Update button states
    document.getElementById('setPinBtn').disabled = !isValid;
    document.getElementById('changePinBtn').disabled = !isValid;
    document.getElementById('verifyPinBtn').disabled = !isValid;
}

// PIN validation
function validatePinMatch(pin1Id, pin2Id) {
    const pin1 = document.getElementById(pin1Id).value;
    const pin2 = document.getElementById(pin2Id).value;
    
    if (pin1 && pin2 && pin1 !== pin2) {
        return false;
    }
    return true;
}

// Form validations
document.getElementById('setPinForm').addEventListener('submit', function(e) {
    if (!validatePinMatch('new_pin_set', 'confirm_pin_set')) {
        e.preventDefault();
        alert('PIN confirmation does not match');
        return;
    }
});

document.getElementById('changePinForm').addEventListener('submit', function(e) {
    if (!validatePinMatch('new_pin_change', 'confirm_pin_change')) {
        e.preventDefault();
        alert('New PIN confirmation does not match');
        return;
    }
});

// Real-time PIN validation
document.getElementById('confirm_pin_set').addEventListener('input', function() {
    const isMatch = validatePinMatch('new_pin_set', 'confirm_pin_set');
    this.classList.toggle('is-invalid', !isMatch && this.value.length === 4);
    this.classList.toggle('is-valid', isMatch && this.value.length === 4);
});

document.getElementById('confirm_pin_change').addEventListener('input', function() {
    const isMatch = validatePinMatch('new_pin_change', 'confirm_pin_change');
    this.classList.toggle('is-invalid', !isMatch && this.value.length === 4);
    this.classList.toggle('is-valid', isMatch && this.value.length === 4);
});

// Ensure only numbers are entered
document.querySelectorAll('input[type="password"]').forEach(input => {
    input.addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
        if (this.value.length > 4) {
            this.value = this.value.slice(0, 4);
        }
    });
});

// Auto-fill account number from URL parameter if present
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const accountNumber = urlParams.get('account');
    if (accountNumber) {
        document.getElementById('account_number').value = accountNumber;
        // Trigger the blur event to load account info
        document.getElementById('account_number').dispatchEvent(new Event('blur'));
    }
});
</script>
{% endblock %}
