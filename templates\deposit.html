{% extends "base.html" %}

{% block title %}Deposit - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-plus-circle"></i> Make Deposit
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill-wave"></i> Deposit Funds
            </div>
            <div class="card-body">
                <form method="POST" id="depositForm">
                    <div class="mb-3">
                        <label for="account_number" class="form-label">
                            <i class="fas fa-hashtag"></i> Account Number *
                        </label>
                        <input type="number" class="form-control" id="account_number" name="account_number" required>
                        <div class="form-text">Enter the account number to deposit to</div>
                        <div id="account-info" class="mt-2"></div>
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">
                            <i class="fas fa-dollar-sign"></i> Deposit Amount *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   min="0.01" step="0.01" max="100000" required>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> Maximum single deposit: ₹1,00,000
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-plus-circle"></i> Deposit Funds
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Information Cards -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Deposit Information
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Instant processing</li>
                    <li><i class="fas fa-check text-success"></i> Maximum deposit: ₹1,00,000</li>
                    <li><i class="fas fa-check text-success"></i> Daily limit: ₹50,000</li>
                    <li><i class="fas fa-check text-success"></i> Transaction logging</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security Features
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-lock text-primary"></i> Secure transaction processing</li>
                    <li><i class="fas fa-history text-primary"></i> Complete audit trail</li>
                    <li><i class="fas fa-database text-primary"></i> Automatic data backup</li>
                    <li><i class="fas fa-check-circle text-primary"></i> Real-time validation</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions (if any) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('withdraw') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-minus-circle"></i> Make Withdrawal
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('transfer') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-exchange-alt"></i> Transfer Funds
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('balance_inquiry') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search-dollar"></i> Check Balance
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Account number validation and info fetching
document.getElementById('account_number').addEventListener('blur', function() {
    const accountNumber = this.value;
    const accountInfo = document.getElementById('account-info');
    
    if (accountNumber) {
        fetch('/api/account/' + accountNumber)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle"></i> Account not found</div>';
                } else {
                    accountInfo.innerHTML = `
                        <div class="alert alert-info alert-sm">
                            <i class="fas fa-user"></i> <strong>${data.name}</strong> (${data.account_type})<br>
                            <i class="fas fa-wallet"></i> Current Balance: ₹${parseFloat(data.balance).toFixed(2)}<br>
                            <i class="fas fa-info-circle"></i> Status: ${data.status}
                        </div>
                    `;
                }
            })
            .catch(error => {
                accountInfo.innerHTML = '<div class="alert alert-warning alert-sm"><i class="fas fa-exclamation-triangle"></i> Unable to fetch account information</div>';
            });
    } else {
        accountInfo.innerHTML = '';
    }
});

// Form validation
document.getElementById('depositForm').addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('amount').value);
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Deposit amount must be positive');
        return;
    }
    
    if (amount > 100000) {
        e.preventDefault();
        alert('Deposit amount cannot exceed ₹1,00,000');
        return;
    }
});

// Format amount input
document.getElementById('amount').addEventListener('input', function() {
    const value = parseFloat(this.value);
    if (value > 100000) {
        this.value = 100000;
    }
});
</script>
{% endblock %}
