{% extends "base.html" %}

{% block title %}Transaction History - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-history"></i> Transaction History
        </h1>
    </div>
</div>

<!-- Account Information -->
{% if account %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-user"></i> Account Information
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Account Number:</strong><br>
                        <span class="h5 text-primary">{{ account.account_number }}</span>
                    </div>
                    <div class="col-md-3">
                        <strong>Account Holder:</strong><br>
                        {{ account.name }}
                    </div>
                    <div class="col-md-3">
                        <strong>Account Type:</strong><br>
                        <span class="badge bg-{{ 'primary' if account.account_type == 'Savings' else 'info' }}">
                            {{ account.account_type }}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>Current Balance:</strong><br>
                        <span class="h5 text-success">₹{{ "%.2f"|format(account.balance) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Transaction History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-list"></i> Transaction History</span>
                {% if transactions %}
                    <span class="badge bg-info">{{ transactions|length }} transaction(s)</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Operation</th>
                                    <th>Amount</th>
                                    <th>Balance After</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>
                                        <small class="text-muted">{{ transaction.timestamp }}</small>
                                    </td>
                                    <td>
                                        {% set operation_class = {
                                            'CREATE': 'success',
                                            'DEPOSIT': 'success',
                                            'WITHDRAW': 'warning',
                                            'TRANSFER_IN': 'info',
                                            'TRANSFER_OUT': 'primary',
                                            'CLOSE': 'danger'
                                        } %}
                                        {% set operation_icon = {
                                            'CREATE': 'fas fa-plus-circle',
                                            'DEPOSIT': 'fas fa-arrow-down',
                                            'WITHDRAW': 'fas fa-arrow-up',
                                            'TRANSFER_IN': 'fas fa-arrow-right',
                                            'TRANSFER_OUT': 'fas fa-arrow-left',
                                            'CLOSE': 'fas fa-times-circle'
                                        } %}
                                        <span class="badge bg-{{ operation_class.get(transaction.operation, 'secondary') }}">
                                            <i class="{{ operation_icon.get(transaction.operation, 'fas fa-circle') }}"></i>
                                            {{ transaction.operation.replace('_', ' ') }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if transaction.amount and transaction.amount != 'None' %}
                                            {% if transaction.operation in ['DEPOSIT', 'TRANSFER_IN', 'CREATE'] %}
                                                <span class="text-success">+₹{{ transaction.amount }}</span>
                                            {% elif transaction.operation in ['WITHDRAW', 'TRANSFER_OUT'] %}
                                                <span class="text-danger">-₹{{ transaction.amount }}</span>
                                            {% else %}
                                                ₹{{ transaction.amount }}
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>₹{{ transaction.balance_after }}</strong>
                                    </td>
                                    <td>
                                        {% if transaction.details %}
                                            <small class="text-muted">{{ transaction.details }}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Transaction History</h5>
                        <p class="text-muted">{{ message if message else 'No transactions found for this account.' }}</p>
                        {% if account %}
                            <div class="mt-3">
                                <a href="{{ url_for('deposit') }}" class="btn btn-success me-2">
                                    <i class="fas fa-plus-circle"></i> Make Deposit
                                </a>
                                <a href="{{ url_for('withdraw') }}" class="btn btn-warning">
                                    <i class="fas fa-minus-circle"></i> Make Withdrawal
                                </a>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Transaction Summary -->
{% if transactions %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">
                    {{ transactions|selectattr('operation', 'in', ['DEPOSIT', 'TRANSFER_IN', 'CREATE'])|list|length }}
                </h5>
                <p class="card-text">Credits</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">
                    {{ transactions|selectattr('operation', 'in', ['WITHDRAW', 'TRANSFER_OUT'])|list|length }}
                </h5>
                <p class="card-text">Debits</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">
                    {{ transactions|selectattr('operation', 'in', ['TRANSFER_IN', 'TRANSFER_OUT'])|list|length }}
                </h5>
                <p class="card-text">Transfers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ transactions|length }}</h5>
                <p class="card-text">Total Transactions</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="row">
                    {% if account %}
                        <div class="col-md-2">
                            <a href="{{ url_for('balance_inquiry') }}?account={{ account.account_number }}" 
                               class="btn btn-outline-primary w-100">
                                <i class="fas fa-search-dollar"></i> Check Balance
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('deposit') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-plus-circle"></i> Deposit
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('withdraw') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-minus-circle"></i> Withdraw
                            </a>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('transfer') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-exchange-alt"></i> Transfer
                            </a>
                        </div>
                    {% endif %}
                    <div class="col-md-2">
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-list"></i> All Accounts
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh functionality (optional)
function refreshHistory() {
    location.reload();
}

// Export functionality (placeholder)
function exportHistory() {
    alert('Export functionality would be implemented here');
}

// Print functionality
function printHistory() {
    window.print();
}

// Add some interactivity to transaction rows
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('click', function() {
            // Could show more details in a modal
            console.log('Transaction row clicked');
        });
    });
});
</script>

<style>
@media print {
    .btn, .card-header, .sidebar, .navbar {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
