#!/usr/bin/env python3
"""
Quick test script to verify Flask app functionality
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_app():
    print("Testing GlobalDigital Bank Flask Application")
    print("=" * 50)
    
    # Test 1: Check if main page loads
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✓ Main page: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Main page failed: {e}")
        return
    
    # Test 2: Check accounts page
    try:
        response = requests.get(f"{BASE_URL}/accounts")
        print(f"✓ Accounts page: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Accounts page failed: {e}")
    
    # Test 3: Check analytics page
    try:
        response = requests.get(f"{BASE_URL}/analytics")
        print(f"✓ Analytics page: Status {response.status_code}")
    except Exception as e:
        print(f"✗ Analytics page failed: {e}")
    
    # Test 4: Test API endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/account/1001")
        if response.status_code == 404:
            print("✓ API endpoint working (account not found as expected)")
        elif response.status_code == 200:
            print("✓ API endpoint working (account found)")
        else:
            print(f"? API endpoint: Status {response.status_code}")
    except Exception as e:
        print(f"✗ API endpoint failed: {e}")
    
    # Test 5: Create account via form simulation
    try:
        session = requests.Session()
        
        # Get the create account page first
        response = session.get(f"{BASE_URL}/create_account")
        print(f"✓ Create account page: Status {response.status_code}")
        
        # Try to create an account
        account_data = {
            'name': 'Test User',
            'age': '25',
            'account_type': 'Savings',
            'initial_deposit': '1000'
        }
        
        response = session.post(f"{BASE_URL}/create_account", data=account_data, allow_redirects=False)
        if response.status_code in [302, 303]:  # Redirect after successful creation
            print("✓ Account creation: Successful (redirected)")
        else:
            print(f"? Account creation: Status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Account creation failed: {e}")
    
    print("\n" + "=" * 50)
    print("Test completed! Check the browser at http://localhost:5000")
    print("All major features should be working correctly.")

if __name__ == "__main__":
    test_app()
