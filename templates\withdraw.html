{% extends "base.html" %}

{% block title %}Withdraw - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-minus-circle"></i> Make Withdrawal
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-money-bill-wave"></i> Withdraw Funds
            </div>
            <div class="card-body">
                <form method="POST" id="withdrawForm">
                    <div class="mb-3">
                        <label for="account_number" class="form-label">
                            <i class="fas fa-hashtag"></i> Account Number *
                        </label>
                        <input type="number" class="form-control" id="account_number" name="account_number" required>
                        <div class="form-text">Enter your account number</div>
                        <div id="account-info" class="mt-2"></div>
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">
                            <i class="fas fa-dollar-sign"></i> Withdrawal Amount *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   min="0.01" step="0.01" required>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-exclamation-triangle text-warning"></i> 
                            Minimum balance must be maintained (Savings: ₹500, Current: ₹1,000)
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> Global minimum balance: ₹500
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-warning btn-lg">
                            <i class="fas fa-minus-circle"></i> Withdraw Funds
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Information Cards -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Withdrawal Information
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Instant processing</li>
                    <li><i class="fas fa-check text-success"></i> Daily limit: ₹50,000</li>
                    <li><i class="fas fa-shield-alt text-primary"></i> Global minimum: ₹500</li>
                    <li><i class="fas fa-history text-primary"></i> Transaction logging</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> Minimum Balance Requirements
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-primary">Savings Account</h6>
                        <p class="small">Minimum Balance: ₹500</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">Current Account</h6>
                        <p class="small">Minimum Balance: ₹1,000</p>
                    </div>
                </div>
                <div class="alert alert-warning alert-sm">
                    <i class="fas fa-info-circle"></i> 
                    Global minimum balance of ₹500 must be maintained regardless of account type.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Quick Actions
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('deposit') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus-circle"></i> Make Deposit
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('transfer') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-exchange-alt"></i> Transfer Funds
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('balance_inquiry') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-search-dollar"></i> Check Balance
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentAccountData = null;

// Account number validation and info fetching
document.getElementById('account_number').addEventListener('blur', function() {
    const accountNumber = this.value;
    const accountInfo = document.getElementById('account-info');
    
    if (accountNumber) {
        fetch('/api/account/' + accountNumber)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle"></i> Account not found</div>';
                    currentAccountData = null;
                } else if (data.status !== 'Active') {
                    accountInfo.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-ban"></i> Account is not active</div>';
                    currentAccountData = null;
                } else {
                    currentAccountData = data;
                    const minBalance = data.account_type === 'Savings' ? 500 : 1000;
                    const globalMin = 500;
                    const effectiveMin = Math.max(minBalance, globalMin);
                    const maxWithdrawal = Math.max(0, data.balance - effectiveMin);
                    
                    accountInfo.innerHTML = `
                        <div class="alert alert-info alert-sm">
                            <i class="fas fa-user"></i> <strong>${data.name}</strong> (${data.account_type})<br>
                            <i class="fas fa-wallet"></i> Current Balance: ₹${parseFloat(data.balance).toFixed(2)}<br>
                            <i class="fas fa-minus-circle"></i> Max Withdrawal: ₹${maxWithdrawal.toFixed(2)}<br>
                            <i class="fas fa-shield-alt"></i> Required Balance: ₹${effectiveMin}
                        </div>
                    `;
                    
                    // Update amount input max value
                    document.getElementById('amount').max = maxWithdrawal;
                }
            })
            .catch(error => {
                accountInfo.innerHTML = '<div class="alert alert-warning alert-sm"><i class="fas fa-exclamation-triangle"></i> Unable to fetch account information</div>';
                currentAccountData = null;
            });
    } else {
        accountInfo.innerHTML = '';
        currentAccountData = null;
    }
});

// Form validation
document.getElementById('withdrawForm').addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('amount').value);
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Withdrawal amount must be positive');
        return;
    }
    
    if (currentAccountData) {
        const minBalance = currentAccountData.account_type === 'Savings' ? 500 : 1000;
        const globalMin = 500;
        const effectiveMin = Math.max(minBalance, globalMin);
        const maxWithdrawal = Math.max(0, currentAccountData.balance - effectiveMin);
        
        if (amount > maxWithdrawal) {
            e.preventDefault();
            alert(`Withdrawal amount exceeds available balance. Maximum withdrawal: ₹${maxWithdrawal.toFixed(2)}`);
            return;
        }
    }
});

// Format amount input
document.getElementById('amount').addEventListener('input', function() {
    if (currentAccountData) {
        const minBalance = currentAccountData.account_type === 'Savings' ? 500 : 1000;
        const globalMin = 500;
        const effectiveMin = Math.max(minBalance, globalMin);
        const maxWithdrawal = Math.max(0, currentAccountData.balance - effectiveMin);
        
        if (parseFloat(this.value) > maxWithdrawal) {
            this.value = maxWithdrawal.toFixed(2);
        }
    }
});
</script>
{% endblock %}
