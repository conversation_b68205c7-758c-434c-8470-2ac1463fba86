{% extends "base.html" %}

{% block title %}Create Account - GlobalDigital Bank{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user-plus"></i> Create New Account
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-form"></i> Account Information
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            <i class="fas fa-user"></i> Full Name *
                        </label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">Enter the account holder's full name</div>
                    </div>

                    <div class="mb-3">
                        <label for="age" class="form-label">
                            <i class="fas fa-birthday-cake"></i> Age *
                        </label>
                        <input type="number" class="form-control" id="age" name="age" min="18" max="150" required>
                        <div class="form-text">Minimum age requirement: 18 years</div>
                    </div>

                    <div class="mb-3">
                        <label for="account_type" class="form-label">
                            <i class="fas fa-piggy-bank"></i> Account Type *
                        </label>
                        <select class="form-select" id="account_type" name="account_type" required onchange="updateMinBalance()">
                            <option value="">Select Account Type</option>
                            <option value="Savings">Savings Account</option>
                            <option value="Current">Current Account</option>
                        </select>
                        <div class="form-text" id="min-balance-info">Select account type to see minimum balance requirement</div>
                    </div>

                    <div class="mb-3">
                        <label for="initial_deposit" class="form-label">
                            <i class="fas fa-money-bill-wave"></i> Initial Deposit *
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="initial_deposit" name="initial_deposit" 
                                   min="0" step="0.01" required>
                        </div>
                        <div class="form-text" id="deposit-info">Enter the initial deposit amount</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus-circle"></i> Create Account
                        </button>
                        <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Accounts
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Information Cards -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Account Types
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-primary">Savings Account</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success"></i> Minimum Balance: ₹500</li>
                            <li><i class="fas fa-check text-success"></i> Interest Earning</li>
                            <li><i class="fas fa-check text-success"></i> Personal Use</li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <h6 class="text-info">Current Account</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success"></i> Minimum Balance: ₹1,000</li>
                            <li><i class="fas fa-check text-success"></i> Business Use</li>
                            <li><i class="fas fa-check text-success"></i> Higher Limits</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Requirements
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Minimum age: 18 years</li>
                    <li><i class="fas fa-check text-success"></i> Valid name required</li>
                    <li><i class="fas fa-check text-success"></i> Initial deposit as per account type</li>
                    <li><i class="fas fa-check text-success"></i> Maximum single deposit: ₹1,00,000</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateMinBalance() {
    const accountType = document.getElementById('account_type').value;
    const minBalanceInfo = document.getElementById('min-balance-info');
    const depositInfo = document.getElementById('deposit-info');
    const initialDepositInput = document.getElementById('initial_deposit');
    
    if (accountType === 'Savings') {
        minBalanceInfo.innerHTML = '<i class="fas fa-info-circle text-primary"></i> Minimum balance for Savings Account: ₹500';
        depositInfo.innerHTML = 'Minimum initial deposit: ₹500';
        initialDepositInput.min = '500';
    } else if (accountType === 'Current') {
        minBalanceInfo.innerHTML = '<i class="fas fa-info-circle text-info"></i> Minimum balance for Current Account: ₹1,000';
        depositInfo.innerHTML = 'Minimum initial deposit: ₹1,000';
        initialDepositInput.min = '1000';
    } else {
        minBalanceInfo.innerHTML = 'Select account type to see minimum balance requirement';
        depositInfo.innerHTML = 'Enter the initial deposit amount';
        initialDepositInput.min = '0';
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const accountType = document.getElementById('account_type').value;
    const initialDeposit = parseFloat(document.getElementById('initial_deposit').value);
    
    if (accountType === 'Savings' && initialDeposit < 500) {
        e.preventDefault();
        alert('Initial deposit for Savings Account must be at least ₹500');
        return;
    }
    
    if (accountType === 'Current' && initialDeposit < 1000) {
        e.preventDefault();
        alert('Initial deposit for Current Account must be at least ₹1,000');
        return;
    }
    
    if (initialDeposit > 100000) {
        e.preventDefault();
        alert('Initial deposit cannot exceed ₹1,00,000');
        return;
    }
});
</script>
{% endblock %}
